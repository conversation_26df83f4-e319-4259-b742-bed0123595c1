"""
Progress tracking service for KPI job processing.

This module provides a comprehensive progress tracking system that integrates
with the job service workflow to provide real-time progress updates with
percentage calculations based on periods and processing steps.
"""

import logging
import time
from typing import Optional, Callable, Dict, Any, List
from enum import Enum

from src.models.kpi import JobParameters
from src.models.axis import Period
from src.utils.job_api_client import set_job_progress_message
from src.services.base_service import BaseService


class ProcessingStage(Enum):
    """Enumeration of processing stages for progress calculation."""

    INITIALIZATION = "initialization"
    VALIDATION = "validation"
    QUERY_GENERATION = "query_generation"
    QUERY_EXECUTION = "query_execution"
    DATA_STORAGE = "data_storage"
    FINALIZATION = "finalization"


class ProgressTracker(BaseService):
    """
    Service for tracking and reporting job progress in real-time.

    This service provides comprehensive progress tracking throughout the job
    processing lifecycle, including percentage calculations and real-time
    API updates.
    """

    def __init__(
        self,
        job_id: int,
        total_periods: int = 1,
        msg_logger_func: Optional[Callable] = None,
        enable_api_updates: bool = True,
    ):
        """
        Initialize the progress tracker.

        Args:
            job_id: The job ID for API updates
            total_periods: Total number of periods to process
            msg_logger_func: Optional function for logging messages to the UI
            enable_api_updates: Whether to send progress updates to the API
        """
        super().__init__(msg_logger_func)

        self.job_id = job_id
        self.total_periods = total_periods
        self.enable_api_updates = enable_api_updates

        # Progress tracking state
        self.current_period_index = 0
        self.current_stage = ProcessingStage.INITIALIZATION
        self.stage_progress = 0  # Progress within current stage (0-100)

        # Stage weights for overall progress calculation
        self.stage_weights = {
            ProcessingStage.INITIALIZATION: 5,
            ProcessingStage.VALIDATION: 10,
            ProcessingStage.QUERY_GENERATION: 15,
            ProcessingStage.QUERY_EXECUTION: 60,
            ProcessingStage.DATA_STORAGE: 8,
            ProcessingStage.FINALIZATION: 2,
        }

        # Query execution sub-stages for detailed tracking
        self.query_stages = [
            "pre_axis",
            "axis",
            "buyers",
            "buyers_final",
            "buyers_uplift_factor",
            "final",
            "result_table",
        ]

        self.current_query_stage_index = 0
        self.start_time = time.time()

        self.logger.info(
            f"Initialized progress tracker for job {job_id} with {total_periods} periods"
        )

    def calculate_overall_progress(self) -> int:
        """
        Calculate overall progress percentage (0-100).

        Returns:
            Progress percentage as integer
        """
        # Calculate progress for completed periods
        completed_periods_progress = (
            self.current_period_index / self.total_periods
        ) * 100

        # Calculate progress for current period
        current_period_weight = 100 / self.total_periods

        # Calculate stage progress within current period
        total_stage_weight = sum(self.stage_weights.values())

        # Find completed stages (stages that come before current stage)
        stage_order = list(ProcessingStage)
        current_stage_index = stage_order.index(self.current_stage)

        completed_stages_weight = sum(
            self.stage_weights[stage] for stage in stage_order[:current_stage_index]
        )

        current_stage_weight = self.stage_weights[self.current_stage]
        current_stage_progress = (
            completed_stages_weight + (current_stage_weight * self.stage_progress / 100)
        ) / total_stage_weight

        current_period_progress = current_stage_progress * current_period_weight

        total_progress = completed_periods_progress + current_period_progress

        return min(100, max(0, int(total_progress)))

    def update_stage(
        self, stage: ProcessingStage, message: str, stage_progress: int = 0
    ):
        """
        Update the current processing stage.

        Args:
            stage: The new processing stage
            message: Progress message to display
            stage_progress: Progress within the stage (0-100)
        """
        self.current_stage = stage
        self.stage_progress = stage_progress

        overall_progress = self.calculate_overall_progress()

        # Format message with period info if processing multiple periods
        if self.total_periods > 1:
            period_info = (
                f"Period {self.current_period_index + 1}/{self.total_periods}: "
            )
            full_message = f"{period_info}{message}"
        else:
            full_message = message

        self.log_message(f"[{overall_progress}%] {full_message}")

        # Send API update if enabled
        if self.enable_api_updates:
            try:
                set_job_progress_message(self.job_id, full_message, overall_progress)
            except Exception as e:
                self.logger.warning(f"Failed to send progress update to API: {e}")

    def update_query_stage(self, query_stage: str, message: Optional[str] = None):
        """
        Update progress within query execution stage.

        Args:
            query_stage: Name of the query stage being executed
            message: Optional custom message
        """
        if query_stage in self.query_stages:
            self.current_query_stage_index = self.query_stages.index(query_stage)

        # Calculate progress within query execution stage
        query_progress = int(
            (self.current_query_stage_index / len(self.query_stages)) * 100
        )

        default_message = f"Executing {query_stage} query"
        display_message = message or default_message

        self.update_stage(
            ProcessingStage.QUERY_EXECUTION, display_message, query_progress
        )

    def start_period(self, period: Period, period_index: int):
        """
        Start processing a new period.

        Args:
            period: The period being processed
            period_index: Index of the period (0-based)
        """
        self.current_period_index = period_index
        self.current_query_stage_index = 0

        message = f"Starting processing for period: {period.label}"
        self.update_stage(ProcessingStage.INITIALIZATION, message, 100)

    def complete_period(self, period: Period):
        """
        Mark a period as completed.

        Args:
            period: The completed period
        """
        message = f"Completed processing for period: {period.label}"
        self.log_message(message)

    def update_metadata(self, metadata_type: str, value: Any):
        """
        Report metadata updates (like query_ids).

        Args:
            metadata_type: Type of metadata being updated
            value: The metadata value
        """
        message = f"Updated {metadata_type}: {value}"
        self.logger.debug(message)

    def finalize(self, success: bool, error_message: Optional[str] = None):
        """
        Finalize progress tracking.

        Args:
            success: Whether the job completed successfully
            error_message: Optional error message if job failed
        """
        if success:
            self.update_stage(
                ProcessingStage.FINALIZATION, "Job completed successfully", 100
            )
        else:
            error_msg = (
                f"Job failed: {error_message}" if error_message else "Job failed"
            )
            self.log_message(f"[ERROR] {error_msg}")

            if self.enable_api_updates:
                try:
                    set_job_progress_message(self.job_id, error_msg, 0)
                except Exception as e:
                    self.logger.warning(f"Failed to send error update to API: {e}")

        duration = time.time() - self.start_time
        self.logger.info(
            f"Progress tracking completed for job {self.job_id} in {duration:.2f} seconds"
        )
