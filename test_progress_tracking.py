#!/usr/bin/env python3
"""
Test script for the integrated progress tracking system.

This script validates that the refactored system provides real-time progress updates,
correctly tracks metadata updates, and maintains compatibility with existing functionality.
"""

import logging
import time
from typing import List

from src.services.progress_tracker import ProgressTracker, ProcessingStage
from src.models.axis import Period
from src.utils.job_api_client import set_job_progress_message

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_progress_tracker_basic_functionality():
    """Test basic progress tracker functionality."""
    print("\n=== Testing Basic Progress Tracker Functionality ===")

    # Create a test progress tracker (disable API updates for testing)
    tracker = ProgressTracker(
        job_id=999999,  # Test job ID
        total_periods=2,
        enable_api_updates=False,  # Disable API calls for testing
    )

    # Test stage progression
    stages = [
        (ProcessingStage.INITIALIZATION, "Starting initialization", 0),
        (ProcessingStage.VALIDATION, "Validating data", 50),
        (ProcessingStage.QUERY_GENERATION, "Generating queries", 100),
        (ProcessingStage.QUERY_EXECUTION, "Executing queries", 0),
        (ProcessingStage.DATA_STORAGE, "Storing data", 75),
        (ProcessingStage.FINALIZATION, "Finalizing", 100),
    ]

    for stage, message, progress in stages:
        tracker.update_stage(stage, message, progress)
        overall_progress = tracker.calculate_overall_progress()
        print(
            f"Stage: {stage.value}, Progress: {progress}%, Overall: {overall_progress}%"
        )
        time.sleep(0.1)  # Small delay to simulate processing

    # Test period progression
    print("\n--- Testing Period Progression ---")
    test_periods = [
        Period(label="2023-01", date_start="2023-01-01", date_end="2023-01-31"),
        Period(label="2023-02", date_start="2023-02-01", date_end="2023-02-28"),
    ]

    for i, period in enumerate(test_periods):
        tracker.start_period(period, i)
        tracker.update_stage(
            ProcessingStage.QUERY_EXECUTION, f"Processing {period.label}", 50
        )
        tracker.complete_period(period)

    # Test finalization
    tracker.finalize(True)
    print("✓ Basic progress tracker functionality test completed")


def test_query_stage_tracking():
    """Test query stage tracking within query execution."""
    print("\n=== Testing Query Stage Tracking ===")

    tracker = ProgressTracker(job_id=999998, total_periods=1, enable_api_updates=False)

    # Test query stages
    query_stages = [
        "pre_axis",
        "axis",
        "buyers",
        "buyers_final",
        "buyers_uplift_factor",
        "final",
        "result_table",
    ]

    tracker.update_stage(ProcessingStage.QUERY_EXECUTION, "Starting query execution", 0)

    for stage in query_stages:
        tracker.update_query_stage(stage)
        overall_progress = tracker.calculate_overall_progress()
        print(f"Query Stage: {stage}, Overall Progress: {overall_progress}%")
        time.sleep(0.1)

    print("✓ Query stage tracking test completed")


def test_metadata_updates():
    """Test metadata update tracking."""
    print("\n=== Testing Metadata Updates ===")

    tracker = ProgressTracker(job_id=999997, total_periods=1, enable_api_updates=False)

    # Test metadata updates
    test_metadata = [
        ("query_ids", ["query_123", "query_456"]),
        ("result_rows", 1500),
        ("table_name", "kpi_results.data_test_123"),
    ]

    for metadata_type, value in test_metadata:
        tracker.update_metadata(metadata_type, value)
        print(f"Updated metadata: {metadata_type} = {value}")

    print("✓ Metadata updates test completed")


def test_progress_calculation():
    """Test progress calculation accuracy."""
    print("\n=== Testing Progress Calculation Accuracy ===")

    # Test with different period counts
    for period_count in [1, 2, 5]:
        print(f"\n--- Testing with {period_count} periods ---")
        tracker = ProgressTracker(
            job_id=999996, total_periods=period_count, enable_api_updates=False
        )

        # Test progress at different stages
        test_scenarios = [
            (ProcessingStage.INITIALIZATION, 100, "Initialization complete"),
            (ProcessingStage.VALIDATION, 100, "Validation complete"),
            (ProcessingStage.QUERY_GENERATION, 100, "Query generation complete"),
            (ProcessingStage.QUERY_EXECUTION, 50, "Query execution halfway"),
            (ProcessingStage.DATA_STORAGE, 100, "Data storage complete"),
            (ProcessingStage.FINALIZATION, 100, "Finalization complete"),
        ]

        # Simulate processing all periods
        for period_idx in range(period_count):
            tracker.current_period_index = period_idx

            for stage, stage_progress, message in test_scenarios:
                tracker.update_stage(stage, message, stage_progress)
                overall = tracker.calculate_overall_progress()
                if period_idx == 0:  # Only print for first period to avoid spam
                    print(f"  {stage.value}: {overall}%")

        # Verify final progress is 100% (should be 100% after processing all periods)
        final_progress = tracker.calculate_overall_progress()
        expected_final = (
            100 if period_count == 1 else int((period_count / period_count) * 100)
        )
        assert final_progress >= 98, (
            f"Expected ~100% but got {final_progress}% for {period_count} periods"
        )

    print("✓ Progress calculation accuracy test completed")


def test_error_handling():
    """Test error handling and finalization."""
    print("\n=== Testing Error Handling ===")

    tracker = ProgressTracker(job_id=999995, total_periods=1, enable_api_updates=False)

    # Test successful finalization
    tracker.finalize(True)
    print("✓ Successful finalization test completed")

    # Test error finalization
    tracker_error = ProgressTracker(
        job_id=999994, total_periods=1, enable_api_updates=False
    )

    tracker_error.finalize(False, "Test error occurred")
    print("✓ Error finalization test completed")


def test_api_integration():
    """Test API integration (optional - requires API to be available)."""
    print("\n=== Testing API Integration (Optional) ===")

    try:
        # Test a simple API call
        result = set_job_progress_message(999993, "API integration test", 50)
        if "error" not in result:
            print("✓ API integration test completed successfully")
        else:
            print(f"⚠ API integration test failed: {result['error']}")
    except Exception as e:
        print(f"⚠ API integration test skipped (API not available): {e}")


def run_all_tests():
    """Run all progress tracking tests."""
    print("Starting Progress Tracking System Validation Tests")
    print("=" * 60)

    try:
        test_progress_tracker_basic_functionality()
        test_query_stage_tracking()
        test_metadata_updates()
        test_progress_calculation()
        test_error_handling()
        test_api_integration()

        print("\n" + "=" * 60)
        print("✅ All progress tracking tests completed successfully!")
        print("The integrated progress tracking system is working correctly.")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.error("Test failed", exc_info=True)
        return False

    return True


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
