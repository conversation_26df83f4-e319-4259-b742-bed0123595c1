{# Simplified render_axis_join macro without start_pos and end_pos #}
{% macro render_axis_join(axis_key, axis_data) %}
{% if axis_data["type"] == "axsh" %}
    INNER JOIN (
    {# Add CTE if available #}
    {% if axis_data["ddl"]["cte"] %}
        WITH cte_table AS (
        SELECT * FROM {{ axis_data["ddl"]["cte"][13:-1] }} AND dt_record BETWEEN '{{ period_start }}' AND toStartOfMonth(toDate('{{ period_end }}'))) ct
        INNER JOIN (
            SELECT hhkey, MAX(dt_start) AS dt_start
            FROM pet.hh_weights_fullmass
            WHERE dt_end BETWEEN '{{ period_start }}' AND '{{ period_end }}'
            GROUP BY hhkey
        ) hh ON ct.hhkey = hh.hhkey AND ct.dt_record = hh.dt_start)
    {% endif %}

    {# Add queries with UNION ALL #}
    {% if axis_data["ddl"]["queries"]|length > 0 %}
        {{ axis_data["ddl"]["queries"][0] }}
        {% for query in axis_data["ddl"]["queries"][1:] %}
            UNION ALL
            {{ query }}
        {% endfor %}
    {% endif %}
    ) {{ axis_key }} USING (hhkey)
{% endif %}
{% endmacro %}


{# Adding houshold filter and houseshold axis #}
WITH add_axsh AS (
SELECT
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] == "axsh" %}
            concat(toString(position_number), '|', toString(autolabel)) AS {{ axis_key }}_position_number,
            {% else %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endif %}
{% endfor %}
rwbasis,
{% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
    countDistinctIf(hhkey, buyers_ww != 0) AS buyers_raw,
{% endif %}
{% if facts_axis|selectattr("code_name", "equalto", "trips_raw")|list %}
    sum(a.trips_raw) AS trips_raw,
{% endif %}
{% if facts_axis|selectattr("code_name", "in", ["frequency", "packs_per_trip", "trips_000", "spend_per_trip", "volume_per_trip"])|list or (facts_axis|selectattr("code_name", "in", ["repeaters_000", "repeat_rate", "penetration_repeaters", "trial_000"])|list and "axsh" in axes.values()|map(attribute="type")) %}
    sum(trips_fullmass) AS trips,
{% endif %}
{% if facts_axis|selectattr("code_name", "in", ["penetration", "frequency", "buyers_000", "spend_per_buyer", "volume_per_buyer", "repeat_rate"])|list %}
    sum(buyers_ww) AS buyers,
{% endif %}
{% if facts_axis|selectattr("code_name", "in", ["repeaters_000", "repeat_rate", "penetration_repeaters", "trial_000"])|list and ("axsh" in axes.values()|map(attribute="type") or  "flth" in filters.values()|map(attribute="type")) %}
    sum(trips_ww) AS trips_ww,
    sum(buyers_ww * (a.trips_raw = 1)) AS trial_ww,
    (buyers - trial_ww) AS repeaters_ww,
{% endif %}
{% for fact in required_facts %}
    {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo", "volume_loyalty_base", "value_loyalty_base")|list %}
        sum({{ fact }}) AS {{ fact }},
    {% endif %}
{% endfor %}
{% if "BUF" in required_facts or "population" in required_facts %}
    any(p.population) AS population,
{% endif %}
any(projectc) AS projectc
FROM buyers_final a
{% if "BUF" in required_facts or "population" in required_facts or "axsh" in axes.values()|map(attribute="type") or "flth" in filters.values()|map(attribute="type") %}
    INNER JOIN (
        WITH population AS (
        SELECT
        hhkey,
        sum(fullmass) / (dateDiff('month', toDate('{{ period_start }}'), toDate('{{ period_end }}')) + 1) AS weight_wave
        FROM pet.hh_weights_fullmass
        WHERE (id_panel={{ id_panel }}) AND (dt_start >= '{{ period_start }}') AND (dt_end <= '{{ period_end }}')
        GROUP BY hhkey
        )
        SELECT
            hhkey,
            sum(weight_wave)
            {% if "axsh" in axes.values()|map(attribute="type") %}
                OVER (PARTITION BY position_number) AS population,
            autolabel,
            position_number
                {% else %}
                OVER ()AS population
            {% endif %}
        FROM population
        {% if "axsh" in axes.values()|map(attribute="type") %}
            {% for axis_key, axis_data in axes.items() %}
                {% if axis_data["type"] is not none %}
                    {{ render_axis_join(axis_key, axis_data) }}
                {% endif %}
            {% endfor %}
        {% endif %}
        {% for filter_key, filter_data in filters.items() %}
            {% if filter_data["type"] == "flth" %}
                WHERE hhkey IN
                (SELECT hhkey FROM (
                    {% if filter_data["ddl"]["cte"] %}
                        WITH {{ filter_data["ddl"]["cte"] }}
                    {% endif %}
                    {% if filter_data["ddl"]["queries"]|length > 0 %}
                        {{ filter_data["ddl"]["queries"][0] }}
                        {% for query in filter_data["ddl"]["queries"][1:] %}
                            UNION ALL
                            {{ query }}
                        {% endfor %}
                    {% endif %}
                    ) filter_hh
                    INNER JOIN (
                        SELECT hhkey, MAX(dt_start) AS dt_start
                        FROM pet.hh_weights_fullmass 
                        WHERE dt_end BETWEEN '{{ period_start }}' AND '{{ period_end }}'
                        GROUP BY hhkey
                    ) right_date ON filter_hh.hhkey = right_date.hhkey AND filter_hh.dt_year = right_date.dt_start
                )
            {% endif %}
        {% endfor %}
    )  p USING (hhkey)
    {% elif "axsh" in axes.values()|map(attribute="type") %}
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ render_axis_join(axis_key, axis_data) }}
        {% endif %}
    {% endfor %}
{% endif %}
GROUP BY rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        , {{ axis_key }}_position_number
    {% endif %}
{% endfor %}
)
{# Calculating KPI #}
SELECT
{% for fact in required_facts %}
    {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
        sum(a.{{ fact }}) AS {{ fact }},
    {% elif fact == "population" %}
        any({{ fact }}) AS {{ fact }},
    {% elif fact == "volume_loyalty_base" %}
        sum(a.volume_rp + (a.volume_loyalty_base - a.volume_rp) * BUF) AS volume_loyalty_base,
    {% elif fact == "value_loyalty_base" %}
        sum(a.value_rp + (a.value_loyalty_base - a.value_rp) * BUF) AS value_loyalty_base,
    {% endif %}
{% endfor %}
{% if facts_axis|selectattr("code_name", "equalto", "buyers_raw")|list %}
    sum(buyers_raw) AS buyers_raw,
{% endif %}
{% if facts_axis|selectattr("code_name", "equalto", "trips_raw")|list %}
    sum(trips_raw) AS trips_raw,
{% endif %}
{% if facts_axis|selectattr("code_name", "in", ["trips_000", "frequency", "spend_per_trip", "packs_per_trip", "volume_per_trip"])|list %}
    sum(trips) AS trips,
{% endif %}
{% if facts_axis|selectattr("code_name", "in", ["buyers_000", "penetration", "frequency", "spend_per_buyer", "volume_per_buyer", "repeat_rate"])|list %}
    sum(buyers * BUF) AS buyers,
{% endif %}
{% if facts_axis|selectattr("code_name", "in", ["repeaters_000", "repeat_rate", "penetration_repeaters", "trial_000"])|list %}
    sum(BUF * (trial_ww * p_trial_trial + repeaters_ww * p_trial_rep)) AS trial,
    sum(BUF * (repeaters_ww * p_rep_rep + trial_ww * p_rep_trial)) AS repeaters,
{% endif %}
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {{ axis_key }}_position_number,
    {% endif %}
{% endfor %}
any(projectc) as projectc
FROM add_axsh a
{% if "BUF" in required_facts %}
    LEFT JOIN Buyers_Uplift_Factor b USING (rwbasis
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none and axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endfor %}
    )
{% endif %}
GROUP BY
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if not loop.first %}, {% endif %}
            {{ axis_key }}_position_number
        {% endif %}
    {% endfor %}
