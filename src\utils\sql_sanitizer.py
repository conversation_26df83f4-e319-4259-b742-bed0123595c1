import re
import logging
from typing import Dict, Any, Optional, Union, Tuple, List

# Import the SQLFluff formatter
from src.utils.sql_formatter import format_sql

# Set up logging
logger = logging.getLogger(__name__)


def sanitize_identifier(identifier: str) -> str:
    """Sanitize a SQL identifier (table name, column name, etc.)."""
    if not identifier:
        raise ValueError("Identifier cannot be empty")

    # Check for dangerous patterns
    dangerous_patterns = ["drop", "delete", "truncate", "alter", "exec", "--"]
    for pattern in dangerous_patterns:
        if pattern in identifier.lower():
            raise ValueError(f"Identifier contains dangerous pattern: {pattern}")

    # Replace invalid characters with underscores
    sanitized = re.sub(r"[^a-zA-Z0-9_.]", "_", identifier)

    # Ensure identifier doesn't start with a number
    if sanitized and sanitized[0].isdigit():
        sanitized = "_" + sanitized

    return sanitized


def sanitize_string_literal(value: Optional[str]) -> str:
    """Sanitize a string literal for use in SQL."""
    if value is None:
        return "NULL"

    # Escape single quotes by doubling them
    escaped = value.replace("'", "''")

    # Escape percent signs by doubling them (for LIKE clauses)
    escaped = escaped.replace("%", "%%")

    return f"'{escaped}'"


def sanitize_numeric_literal(value: Optional[Union[int, float]]) -> str:
    """Sanitize a numeric literal for use in SQL."""
    if value is None:
        return "NULL"

    if not isinstance(value, (int, float)):
        raise ValueError(f"Value is not a number: {value}")

    return str(value)


def sanitize_value(value: Any) -> str:
    """Sanitize a value based on its type."""
    if value is None:
        return "NULL"

    # Handle booleans first (since bool is a subclass of int)
    if isinstance(value, bool):
        return "1" if value else "0"  # Return string "1" or "0"

    if isinstance(value, str):
        return sanitize_string_literal(value)

    if isinstance(value, (int, float)):
        return sanitize_numeric_literal(value)

    if isinstance(value, (list, tuple)):
        sanitized_items = [sanitize_value(item) for item in value]
        return f"({', '.join(sanitized_items)})"

    # Default to string representation
    return sanitize_string_literal(str(value))


def sanitize_sql(sql: str, params: Optional[Dict[str, Any]] = None) -> str:
    """Sanitize a SQL query with parameters."""
    if not sql:
        raise ValueError("SQL query cannot be empty")

    if not params:
        return sql

    # Replace named parameters with their sanitized values
    sanitized_sql = sql
    for name, value in params.items():
        placeholder = f":{name}"
        sanitized_value = sanitize_value(value)
        sanitized_sql = sanitized_sql.replace(placeholder, sanitized_value)

    return sanitized_sql


def validate_sql(sql: str) -> Tuple[bool, Optional[str]]:
    """Validate a SQL query."""
    if not sql:
        return False, "SQL query cannot be empty"

    # Check for balanced parentheses
    if sql.count("(") != sql.count(")"):
        return False, "Unbalanced parentheses in SQL query"

    # Check for balanced quotes
    if sql.count("'") % 2 != 0:
        return False, "Unbalanced quotes in SQL query"

    # Check for dangerous patterns
    dangerous_patterns = [";", "--", "/*", "*/", "xp_", "sp_", "exec", "execute"]
    for pattern in dangerous_patterns:
        if pattern in sql.lower():
            return False, f"SQL query contains dangerous pattern: {pattern}"

    return True, None


def normalize_sql(sql: str) -> str:
    """
    Normalize a SQL query for better readability using SQLFluff.

    Args:
        sql: SQL query to normalize

    Returns:
        Normalized SQL query
    """
    if not sql:
        return ""

    try:
        # Use SQLFluff to format the SQL query
        formatted_sql, success = format_sql(sql, dialect="clickhouse")

        if success:
            logger.debug("SQL query formatted successfully with SQLFluff")
            return formatted_sql
        else:
            logger.warning(
                "SQLFluff formatting failed, falling back to basic formatting"
            )
            # Fall back to basic formatting if SQLFluff fails
            return _basic_normalize_sql(sql)

    except Exception as e:
        logger.error(f"Error formatting SQL query with SQLFluff: {e}")
        # Fall back to basic formatting if an exception occurs
        return _basic_normalize_sql(sql)


def _basic_normalize_sql(sql: str) -> str:
    """
    Basic SQL normalization without external dependencies.
    Used as a fallback when SQLFluff formatting fails.

    Args:
        sql: SQL query to normalize

    Returns:
        Normalized SQL query
    """
    if not sql:
        return ""

    # Add newlines after common SQL clauses
    clauses = ["SELECT", "FROM", "WHERE", "GROUP BY", "HAVING", "ORDER BY", "LIMIT"]
    normalized = sql

    for clause in clauses:
        pattern = re.compile(f"\\s+{clause}\\s+", re.IGNORECASE)
        normalized = pattern.sub(f"\n{clause} ", normalized)

    # Add indentation for nested queries
    lines = normalized.split("\n")
    result = []
    indent_level = 0

    for line in lines:
        # Increase indent for opening parenthesis
        indent_level += line.count("(") - line.count(")")

        # Add appropriate indentation
        if indent_level > 0:
            result.append("    " * indent_level + line.strip())
        else:
            result.append(line.strip())

    return "\n".join(result)
