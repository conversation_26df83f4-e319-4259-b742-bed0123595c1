"""
Axis service for the application.

This module provides a service for handling axis and filter data processing.
It encapsulates the functionality of the ObjectProcessor and ValidationProcessor classes.
"""

from typing import Optional, Callable

from src.models.kpi import JobParameters
from src.processors.object_processor import ObjectProcessor
from src.processors.validation_processor import ValidationProcessor
from src.services.base_service import BaseService
from src.utils.data_processing import calculate_split_parameters


class AxisService(BaseService):
    """
    Service for handling axis and filter data processing.

    This service encapsulates the functionality of the ObjectProcessor class
    and provides a more service-oriented interface.
    """

    def __init__(
        self,
        mg_api_url: Optional[str] = None,
        mg_api_user: Optional[str] = None,
        mg_api_password: Optional[str] = None,
        msg_logger_func: Optional[Callable] = None,
    ):
        """
        Initialize the axis service.

        Args:
            mg_api_url: Magic Gateway API URL
            mg_api_user: Magic Gateway API username
            mg_api_password: Magic Gateway API password
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)

        # Store API credentials for creating ObjectProcessor instances
        self.mg_api_url = mg_api_url
        self.mg_api_user = mg_api_user
        self.mg_api_password = mg_api_password

        # Create the validation processor
        self.validation_processor = ValidationProcessor()

    def get_all_data(
        self,
        job_parameters: JobParameters,
    ):
        """
        Get all data (axes, filters, and measures) in a single call.

        This method initializes the object processor with all the required parameters
        and returns the populated data.

        Args:
            job_parameters: JobParameters model containing all request parameters

        """
        self.log_message(
            f"Getting all data in a single call: {len(job_parameters.axes)} axes, {len(job_parameters.filters)} filters, {len(job_parameters.facts_axis) if job_parameters.facts_axis else 0} facts"
        )

        # Create a new object processor with all parameters
        object_processor = ObjectProcessor(
            job_parameters=job_parameters,
            mg_api_url=self.mg_api_url,
            mg_api_user=self.mg_api_user,
            mg_api_password=self.mg_api_password,
        )

    def validate_and_prepare_data(
        self,
        job_parameters: JobParameters,
        dialect: str = "clickhouse",
    ):
        """
        Validate and prepare all data for the query builder in a single call.

        This method centralizes all validation and preparation logic before passing
        data to the query builder. It ensures that all parameters are properly validated
        and populated with the necessary information.

        Args:
            job_parameters: JobParameters model containing all request parameters
            dialect: Database dialect to use for the SU fact formula ("clickhouse" or "postgres")

        """
        self.log_message(
            f"Validating and preparing data for {job_parameters.kpi_type.value}"
        )

        # First, validate all data using the validation processor
        self.validation_processor.validate_all(
            job_parameters,
            dialect=dialect,
        )

        # Then, get all data in a single call to populate axes_data, filters_data, and facts_axis in job_parameters
        self.get_all_data(job_parameters=job_parameters)

        # Extract all unique required facts from the populated facts_axis
        # This ensures we capture any required facts that might have been added during processing
        job_parameters.update(
            required_facts=self.validation_processor.extract_required_facts(
                job_parameters.facts_axis or []
            )
        )

        # Calculate split parameters for each period
        for period in job_parameters.periods:
            (
                period.total_positions,
                period.split_axis,
                period.threshold,
            ) = calculate_split_parameters(
                (period.label, period.date_start, period.date_end),
                job_parameters.axes,
                self.logger,
            )
            self.log_message(
                f"Period '{period.label}': total_positions={period.total_positions}, "
                f"split_axis='{period.split_axis}', threshold={period.threshold}"
            )

        self.log_message(
            f"Extracted {len(job_parameters.required_facts)} unique required facts"
        )
        self.log_message(
            f"Processed {len(job_parameters.axes)} axes and {len(job_parameters.filters)} filters"
        )

        return job_parameters
