import os
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, field_validator

from src.models.axis import FactsData
from src.utils.utils import get_poetry_version

# Configure logging
logger = logging.getLogger(__name__)

# Load environment variables from .env file
dotenv_path = os.path.join(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
    ".env",
)
load_dotenv(dotenv_path)


class DefaultMeasures(BaseModel):
    """Default measures list"""

    # Default catman kpis
    DEFAULT_FACTS_AXIS_CATMAN: List[FactsData] = [
        {"id": 78, "relative_axis": None, "relative_position": None},  # Buyers in shop
        {"id": 79, "relative_axis": None, "relative_position": None},  # Value in Store
        {"id": 80, "relative_axis": None, "relative_position": None},  # Trips in Store
        {"id": 81, "relative_axis": None, "relative_position": None},  # Trips Potential
        {
            "id": 82,
            "relative_axis": None,
            "relative_position": None,
        },  # Value Potential (000 Rub)
        {
            "id": 83,
            "relative_axis": None,
            "relative_position": None,
        },  # Value share potential (Propensity)
        {
            "id": 84,
            "relative_axis": None,
            "relative_position": None,
        },  # Value Potential Exploitation
        {
            "id": 85,
            "relative_axis": None,
            "relative_position": None,
        },  # Loyalty Base (000)
        {
            "id": 86,
            "relative_axis": None,
            "relative_position": None,
        },  # Shopper loyalty in store
        {
            "id": 87,
            "relative_axis": None,
            "relative_position": None,
        },  # Buyers Potential (000)
        # Buyers closure rate
        {"id": 88, "relative_axis": None, "relative_position": None},
    ]

    # Default facts_axis parameter as a dictionary where keys are fact IDs and values are property dictionaries
    DEFAULT_FACTS_AXIS: List[FactsData] = [
        {"id": 55, "relative_axis": None, "relative_position": None},  # Value
        {"id": 60, "relative_axis": None, "relative_position": None},  # Volume
        {"id": 61, "relative_axis": None, "relative_position": None},  # Packs
        {"id": 37, "relative_axis": None, "relative_position": None},  # Population
        {"id": 14, "relative_axis": None, "relative_position": None},  # Buyers
        {"id": 35, "relative_axis": None, "relative_position": None},  # Penetration
        {"id": 62, "relative_axis": None, "relative_position": None},  # Volume_Promo
        {"id": 63, "relative_axis": None, "relative_position": None},  # Packs_Promo
        {
            "id": 64,
            "relative_axis": None,
            "relative_position": None,
        },  # Volume_Promo_Share
        {
            "id": 65,
            "relative_axis": None,
            "relative_position": None,
        },  # Volume_Promo_Share_Packs
        {"id": 46, "relative_axis": None, "relative_position": None},  # Spend per Trip
        {
            "id": 76,
            "relative_axis": None,
            "relative_position": None,
        },  # Volume per Trip Packs
        {"id": 2, "relative_axis": None, "relative_position": None},  # Pack_size
        {"id": 6, "relative_axis": None, "relative_position": None},  # Price per volume
        {"id": 8, "relative_axis": None, "relative_position": None},  # Price per volume
        {"id": 10, "relative_axis": None, "relative_position": None},  # Buyers raw
        {"id": 52, "relative_axis": None, "relative_position": None},  # Trips
        {"id": 22, "relative_axis": None, "relative_position": None},  # Frequency
        {"id": 43, "relative_axis": None, "relative_position": None},  # Spend per buyer
        {
            "id": 70,
            "relative_axis": None,
            "relative_position": None,
        },  # Volume per buyer
        {"id": 71, "relative_axis": None, "relative_position": None},  # Packs per buyer
        {"id": 75, "relative_axis": None, "relative_position": None},  # Volume per trip
        {"id": 40, "relative_axis": None, "relative_position": None},  # Repeat Rate
        {"id": 41, "relative_axis": None, "relative_position": None},  # Repeaters
        {
            "id": 36,
            "relative_axis": None,
            "relative_position": None,
        },  # Penetration Repeaters
        {"id": 77, "relative_axis": None, "relative_position": None},  # Trial buyers
        {
            "id": 33,
            "relative_axis": "first_axis",
            "relative_position": 2,
        },  # Loyalty Volume
    ]


class ClickHouseSettings(BaseModel):
    """ClickHouse database connection settings."""

    host: str = Field(default_factory=lambda: os.getenv("CLICKHOUSE_HOST", ""))
    port: int = Field(default_factory=lambda: int(os.getenv("CLICKHOUSE_PORT", "8123")))
    user: str = Field(default_factory=lambda: os.getenv("CLICKHOUSE_USER", ""))
    password: str = Field(default_factory=lambda: os.getenv("CLICKHOUSE_PASSWORD", ""))
    database: str = Field(default_factory=lambda: os.getenv("CLICKHOUSE_DATABASE", ""))

    @field_validator("host")
    @classmethod
    def validate_host(cls, v: str) -> str:
        if not v:
            logger.warning("CLICKHOUSE_HOST is not set")
        return v


class PostgresSettings(BaseModel):
    """PostgreSQL database connection settings."""

    host: str = Field(default_factory=lambda: os.getenv("POSTGRES_HOST", ""))
    port: str = Field(default_factory=lambda: os.getenv("POSTGRES_PORT", ""))
    dbname: str = Field(default_factory=lambda: os.getenv("POSTGRES_APP_DB", ""))
    user: str = Field(default_factory=lambda: os.getenv("POSTGRES_APP_USER", ""))
    password: str = Field(default_factory=lambda: os.getenv("POSTGRES_APP_PWD", ""))

    @field_validator("host")
    @classmethod
    def validate_host(cls, v: str) -> str:
        if not v:
            logger.warning("POSTGRES_HOST is not set")
        return v


class MagicGatewaySettings(BaseModel):
    """Magic Gateway API connection settings."""

    base_url: str = Field(default_factory=lambda: os.getenv("MG_API_URL", ""))
    username: str = Field(default_factory=lambda: os.getenv("MG_API_USER", ""))
    password: str = Field(default_factory=lambda: os.getenv("MG_API_PWD", ""))

    @field_validator("base_url")
    @classmethod
    def validate_base_url(cls, v: str) -> str:
        if not v:
            logger.warning("MG_API_URL is not set")
        return v


class JobApiSettings(BaseModel):
    """Job API connection settings."""

    base_url: str = Field(
        default_factory=lambda: os.getenv("JOB_API_URL", "http://***********:8001")
    )

    @field_validator("base_url")
    @classmethod
    def validate_base_url(cls, v: str) -> str:
        if not v:
            logger.warning("JOB_API_URL is not set")
        return v


class AppSettings(BaseModel):
    """Application settings."""

    output_dir: str = Field(
        default_factory=lambda: os.getenv("OUTPUT_DIR", "/reportoutput/kpi_click")
    )
    memlog_folder: str = Field(
        default_factory=lambda: os.getenv("MEMLOG_FOLDER", "memlog")
    )
    # Get version from poetry or environment variable
    VERSION: str = Field(
        default_factory=lambda: os.getenv("APP_VERSION") or get_poetry_version() or ""
    )


class Config:
    """Configuration class for the application."""

    _instance = None

    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the configuration."""
        if getattr(self, "_initialized", False):
            return

        # Initialize settings models
        self.clickhouse = ClickHouseSettings()
        self.postgres = PostgresSettings()
        self.mg = MagicGatewaySettings()
        self.job_api = JobApiSettings()
        self.app = AppSettings()

        # Initialize su_fact dictionary
        self.su_fact = {
            "PG": {
                "measure": "volume_rp",
                "clickhouse": "if(isNull(pg_su_factor_2020) OR pg_su_factor_2020 = 'NA', 0, toFloat64(pg_su_factor_2020))",
                "postgres": "pgsufac1",
            },
            "Instant Coffees": {
                "measure": "volume_rp",
                "clickhouse": "if(isNull(su_instant_coffee) OR su_instant_coffee = 'NA', 0, toFloat64(su_instant_coffee))",
                "postgres": "suinstan",
            },
            "Henkel": {
                "measure": "volume_rp",
                "clickhouse": "if(isNull(henkel_detergents_washload) OR henkel_detergents_washload = 'NA', 0, toFloat64(henkel_detergents_washload))",
                "postgres": "henkelde",
            },
            "Henkel packs": {
                "measure": "number_rp",
                "clickhouse": "if(isNull(henkel_detergents_washload_num) OR henkel_detergents_washload_num = 'NA', 0, toFloat64(henkel_detergents_washload_num))",
                "postgres": "henkeld0",
            },
            "Henkel adw": {
                "measure": "number_rp",
                "clickhouse": "if(isNull(dwl_washload_num) OR dwl_washload_num = 'NA', 0, toFloat64(dwl_washload_num))",
                "dwl_washload_num": "dwl_washload_num",
            },
        }

        self._initialized = True

    @property
    def clickhouse_host(self) -> str:
        return self.clickhouse.host

    @property
    def clickhouse_port(self) -> int:
        return self.clickhouse.port

    @property
    def clickhouse_user(self) -> str:
        return self.clickhouse.user

    @property
    def clickhouse_password(self) -> str:
        return self.clickhouse.password

    @property
    def clickhouse_database(self) -> str:
        return self.clickhouse.database

    @property
    def postgres_host(self) -> str:
        return self.postgres.host

    @property
    def postgres_port(self) -> str:
        return self.postgres.port

    @property
    def postgres_db(self) -> str:
        return self.postgres.dbname

    @property
    def postgres_user(self) -> str:
        return self.postgres.user

    @property
    def postgres_password(self) -> str:
        return self.postgres.password

    @property
    def mg_api_url(self) -> str:
        return self.mg.base_url

    @property
    def mg_api_user(self) -> str:
        return self.mg.username

    @property
    def mg_api_password(self) -> str:
        return self.mg.password

    @property
    def job_api_url(self) -> str:
        return self.job_api.base_url

    @property
    def output_dir(self) -> str:
        return self.app.output_dir

    @property
    def memlog_folder(self) -> str:
        return self.app.memlog_folder

    @property
    def version(self) -> str:
        return self.app.VERSION

    def get_clickhouse_config(self) -> Dict[str, Any]:
        """Get ClickHouse configuration."""
        return self.clickhouse.model_dump()

    def get_postgres_config(self) -> Dict[str, Any]:
        """Get PostgreSQL configuration."""
        return self.postgres.model_dump()

    def get_mg_config(self) -> Dict[str, Any]:
        """Get Magic Gateway configuration."""
        return self.mg.model_dump()

    def get_job_api_config(self) -> Dict[str, Any]:
        """Get Job API configuration."""
        return self.job_api.model_dump()


# Create a singleton instance
config = Config()
