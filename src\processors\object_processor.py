import logging
import os
import async<PERSON>
from datetime import datetime
from dateutil import relativedel<PERSON>
from typing import Dict, Optional, List, Tuple, Any, Union
from magic_gateway_client import AsyncMagicGatewayClient
from dotenv import load_dotenv
from src.models.axis import AxisData, FilterData, FactsData
from src.core.config import config
from src.models.kpi import JobParameters
from src.utils.data_processing import (
    extract_full_name,
    escape_sql_string,
    run_async_with_event_loop,
)

# Load environment variables from .env file
load_dotenv(
    dotenv_path=os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
        ".env",
    )
)


class ObjectProcessor:
    """Class to handle axis and filter data processing and split parameter calculations.

    This class provides functionality to retrieve axis and filter metadata, including DDL information
    using the Magic Gateway client's get_axis_info and pg_to_clickhouse conversion functionality.
    The axis and filter data is retrieved and DDL information is fetched automatically when the class is initialized.

    For axes, it retrieves axis ID, position count, and label information.
    For filters, it retrieves DDL information that can be used in SQL queries.
    """

    def __init__(
        self,
        job_parameters: JobParameters,
        mg_api_url: Optional[str] = None,
        mg_api_user: Optional[str] = None,
        mg_api_password: Optional[str] = None,
    ):
        """Initialize the ObjectProcessor.

        Args:
            job_parameters: JobParameters model containing all request parameters
            mg_api_url: Magic Gateway API URL (defaults to environment variable)
            mg_api_user: Magic Gateway API username (defaults to environment variable)
            mg_api_password: Magic Gateway API password (defaults to environment variable)
        """
        self.logger = logging.getLogger(self.__class__.__name__)

        # Get API credentials from parameters or environment variables
        self.job_parameters = job_parameters
        self.URL = mg_api_url or os.getenv("MG_API_URL")
        self.USER = mg_api_user or os.getenv("MG_API_USER")
        self.PASSWORD = mg_api_password or os.getenv("MG_API_PWD")

        # Initialize connector and helpers
        if not self.URL or not self.USER or not self.PASSWORD:
            self.logger.error(
                "Missing required credentials for AsyncMagicGatewayClient"
            )
            raise ValueError("Missing required credentials for AsyncMagicGatewayClient")

        self.mg_client = AsyncMagicGatewayClient(
            base_url=self.URL,
            username=self.USER,
            password=self.PASSWORD,
        )

        # Initialize data if axes or filters are provided
        if job_parameters.axes or job_parameters.filters:
            self.initialize()

    # These methods have been moved to ValidationProcessor

    def initialize(self):
        """Initialize axis, filter, and measures data asynchronously.

        This method fetches axis, filter, and measures data using the Magic Gateway client.
        It uses the instance variables for axes, filters, and facts_axis that were set during initialization.
        """
        if (
            not self.job_parameters.axes
            and not self.job_parameters.filters
            and not self.job_parameters.facts_axis
        ):
            self.logger.warning("No axes, filters, or measures to initialize")
            return

        # Use the utility function to handle event loop complexities
        self.logger.info("Fetching data using Magic Gateway client")

        try:
            # Create the coroutine object
            coroutine = self.get_data_with_mg_client(
                self.job_parameters.axes,
                self.job_parameters.filters,
                self.job_parameters.id_panel,
                self.job_parameters.facts_axis,
            )

            # Execute the coroutine using run_async_with_event_loop
            result = run_async_with_event_loop(coroutine)

            # Check if result is valid before accessing it
            if result is None:
                self.logger.error("Failed to fetch data with Magic Gateway client")
                return

            # Ensure result is a dictionary
            if not isinstance(result, dict):
                self.logger.error(f"Expected dictionary result, got {type(result)}")
                # Initialize with empty data
                self.job_parameters.axes = {}
                self.job_parameters.filters = {}
                self.job_parameters.facts_axis = []
                return

            # Update instance variables with the results using safe dictionary access
            self.job_parameters.axes = result.get("axes_data", {})
            self.job_parameters.filters = result.get("filters_data", {})
            self.job_parameters.facts_axis = result.get("measures_data", [])

            self.logger.info(
                f"ObjectProcessor: Successfully initialized with {len(self.job_parameters.axes)} axes, "
                f"{len(self.job_parameters.filters)} filters, and {len(self.job_parameters.facts_axis)} measures"
            )
        except Exception as e:
            self.logger.error(f"Error in initialize: {e}", exc_info=True)
            # Initialize with empty data in case of error
            self.job_parameters.axes = {}
            self.job_parameters.filters = {}
            self.job_parameters.facts_axis = []

    async def fetch_ddl_for_object(
        self, object_name: str, id_panel: int
    ) -> Optional[dict]:
        """
        Fetch DDL information for an object (axis or filter) using mg_client's pg_to_clickhouse conversion.

        Args:
            object_name: Name of the object (axis or filter)
            id_panel: Panel ID to use for table mapping

        Returns:
            Dictionary with 'cte' and 'queries' fields, or None if conversion fails
        """
        try:
            # Use the mg_client to convert PostgreSQL to ClickHouse
            conversion = await self.mg_client.scripts.convert_pg_to_clickhouse(
                object_name=object_name,
                output_mode="click_app",
                id_panel=str(id_panel),
                timeout_seconds=600,
            )

            # Log the response type for debugging
            self.logger.info(
                f"DDL response type for {object_name}: {type(conversion.clickhouse_sql)}"
            )

            # Handle the response based on its type
            if isinstance(conversion.clickhouse_sql, dict):
                # Log the dictionary keys for debugging
                self.logger.info(
                    f"DDL response is a dictionary with keys: {conversion.clickhouse_sql.keys()}"
                )

                # Return the dictionary directly if it has both 'cte' and 'queries' keys
                if (
                    "cte" in conversion.clickhouse_sql
                    and "queries" in conversion.clickhouse_sql
                ):
                    self.logger.info(
                        f"Using dictionary with 'cte' and 'queries' keys. Queries count: {len(conversion.clickhouse_sql['queries'])}"
                    )
                    return {
                        "cte": conversion.clickhouse_sql["cte"],
                        "queries": conversion.clickhouse_sql["queries"],
                    }
                # If it doesn't have both keys, create a default structure
                result = {"cte": "", "queries": []}

                # Extract 'cte' if available
                if "cte" in conversion.clickhouse_sql:
                    result["cte"] = conversion.clickhouse_sql["cte"]

                # Extract 'queries' if available
                if "queries" in conversion.clickhouse_sql:
                    result["queries"] = conversion.clickhouse_sql["queries"]
                elif any(
                    isinstance(v, list) for v in conversion.clickhouse_sql.values()
                ):
                    # Find the first list value and use it as queries
                    for key, value in conversion.clickhouse_sql.items():
                        if isinstance(value, list):
                            result["queries"] = value
                            break

                self.logger.info(
                    f"Created dictionary with 'cte' and 'queries' keys. Queries count: {len(result['queries'])}"
                )
                return result
            elif isinstance(conversion.clickhouse_sql, list):
                # If it's a list, assume it's queries and create a dictionary
                self.logger.info(
                    f"DDL response is a list with {len(conversion.clickhouse_sql)} items, converting to dict"
                )
                return {"cte": "", "queries": conversion.clickhouse_sql}
            elif isinstance(conversion.clickhouse_sql, str):
                # If it's a string, assume it's a query and create a dictionary
                self.logger.info("DDL response is a string, converting to dict")
                return {"cte": "", "queries": [conversion.clickhouse_sql]}
            else:
                self.logger.error(
                    f"Unexpected type for clickhouse_sql: {type(conversion.clickhouse_sql)}"
                )
                return {"cte": "", "queries": []}
        except Exception as e:
            self.logger.error(
                f"Error fetching DDL for object {object_name}: {e}", exc_info=True
            )
            return None

    async def fetch_ddl_for_axis(self, axis_name: str, id_panel: int) -> Optional[dict]:
        """
        Fetch DDL information for an axis using mg_client's pg_to_clickhouse conversion.

        Args:
            axis_name: Name of the axis
            id_panel: Panel ID to use for table mapping

        Returns:
            Dictionary with 'cte' and 'queries' fields, or None if conversion fails
        """
        return await self.fetch_ddl_for_object(axis_name, id_panel)

    async def fetch_ddl_for_filter(
        self, filter_name: str, id_panel: int
    ) -> Optional[dict]:
        """
        Fetch DDL information for a filter using mg_client's pg_to_clickhouse conversion.

        Args:
            filter_name: Name of the filter
            id_panel: Panel ID to use for table mapping

        Returns:
            Dictionary with 'cte' and 'queries' fields, or None if conversion fails
        """
        return await self.fetch_ddl_for_object(filter_name, id_panel)

    async def _populate_object_ddl_fields(
        self, object_values: Dict, id_panel: int, object_type: str, data_dict: Dict
    ):
        """
        Generic method to populate the DDL fields for objects (axes or filters) asynchronously.

        Args:
            object_values: Dictionary mapping object key to object value
            id_panel: Panel ID to use for table mapping
            object_type: Type of object ('axis' or 'filter')
            data_dict: Dictionary to update with DDL information
        """
        if not data_dict or not object_values:
            self.logger.warning(f"No {object_type}s data to populate DDL fields for")
            return

        for obj_key, obj_value in object_values.items():
            if obj_key not in data_dict:
                continue

            # For filters, we need additional check
            if object_type == "filter" and not obj_value:
                continue

            # Extract the object name string from the value
            obj_name = extract_full_name(obj_value, object_type)

            # Skip if we couldn't determine a valid object name
            if not obj_name:
                self.logger.warning(
                    f"Could not determine {object_type} name for {obj_key}, skipping DDL fetch"
                )
                continue

            # Fetch DDL for this object
            fetch_method = (
                self.fetch_ddl_for_axis
                if object_type == "axis"
                else self.fetch_ddl_for_filter
            )
            ddl_result = await fetch_method(obj_name, id_panel)

            # Update the data dictionary with the DDL information
            if ddl_result:
                # Store the DDL dictionary directly
                data_dict[obj_key].ddl = ddl_result
                self.logger.info(
                    f"DDL populated for {object_type} {obj_key} ({len(ddl_result['queries'])} queries)"
                )
            else:
                # Initialize with empty dictionary if no DDL was returned
                data_dict[obj_key].ddl = {"cte": "", "queries": []}
                self.logger.warning(
                    f"Failed to populate DDL for {object_type} {obj_key}"
                )

    async def _populate_axis_ddl_fields(self, axis_values: Dict, id_panel: int):
        """
        Private method to populate the DDL fields for all axes in axes_data asynchronously.

        Args:
            axis_values: Dictionary mapping axis_key to axis value (string, AxisData, or dict)
            id_panel: Panel ID to use for table mapping
        """
        # Use the generic method with 'axis' as object_type
        await self._populate_object_ddl_fields(
            object_values=axis_values,
            id_panel=id_panel,
            object_type="axis",
            data_dict=self.job_parameters.axes,
        )

    async def _populate_filter_ddl_fields(self, filter_dict: Dict, id_panel: int):
        """
        Private method to populate the DDL fields for all filters in filters_data asynchronously.

        Args:
            filter_dict: Dictionary mapping filter_key to filter value (string, FilterData, or dict)
            id_panel: Panel ID to use for table mapping
        """
        # Use the generic method with 'filter' as object_type
        await self._populate_object_ddl_fields(
            object_values=filter_dict,
            id_panel=id_panel,
            object_type="filter",
            data_dict=self.job_parameters.filters,
        )

    async def get_object_data_with_mg_client(
        self, objects: Dict, id_panel: int, object_type: str
    ) -> Dict:
        """
        Generic method to get metadata for objects (axes or filters) using the AsyncMagicGatewayClient.

        For axes, it retrieves axis ID, position count, and label information.
        For filters, it initializes basic filter data.

        Args:
            objects: Dictionary of object key to object value (string or model)
            id_panel: Panel ID to use for table mapping
            object_type: Type of object ('axis' or 'filter')

        Returns:
            Dictionary with object data
        """
        if not objects:
            return {}

        # Determine which data dictionary to use based on object_type
        data_dict = (
            self.job_parameters.axes
            if object_type == "axis"
            else self.job_parameters.filters
        )

        # Reset the data dictionary
        # if object_type == "axis":
        #     self.job_parameters.axes = {}
        #     data_dict = self.job_parameters.axes
        # else:
        #     self.job_parameters.filters = {}
        #     data_dict = self.job_parameters.filters

        # Process each object
        for obj_key, obj_value in objects.items():
            # Skip empty values
            if not obj_value:
                continue

            # Extract the object name string from the value
            obj_name = extract_full_name(obj_value, object_type)

            # Skip if we couldn't determine a valid object name
            if not obj_name:
                self.logger.warning(
                    f"Could not determine {object_type} name for {obj_key}, skipping"
                )
                continue

            # Handle axes and filters differently
            if object_type == "axis":
                try:
                    # Get axis information using the AsyncPostgresModule's get_axis_info method
                    axis_info = await self.mg_client.postgres.get_axis_info(
                        axis_name=obj_name, timeout_seconds=600
                    )

                    # Process labels to escape single quotes in value_group_name
                    processed_labels = []
                    if axis_info.labels:
                        for label in axis_info.labels:
                            # Create a new dict with the same keys but escaped value_group_name
                            processed_label = {}
                            for key, value in label.items():
                                if key == "value_group_name" and value is not None:
                                    processed_label[key] = escape_sql_string(value)
                                else:
                                    processed_label[key] = value
                            processed_labels.append(processed_label)

                    # Get the existing AxisData object if it exists
                    existing_data = objects.get(obj_key)

                    # If the existing object is an AxisData model, update its fields directly
                    if isinstance(existing_data, AxisData):
                        # Update the existing object with the new information
                        existing_data.axis_id = axis_info.axis_id
                        existing_data.axis_positions = (
                            [axis_info.axis_positions]
                            if isinstance(axis_info.axis_positions, int)
                            else axis_info.axis_positions
                        )
                        existing_data.labels = processed_labels
                        existing_data.ddl = {"cte": "", "queries": []}
                        data_dict[obj_key] = existing_data
                    else:
                        # If there's no existing data or it's not an AxisData model,
                        # create a new one with just the retrieved information
                        data_dict[obj_key] = AxisData(
                            axis_id=axis_info.axis_id,
                            axis_positions=[axis_info.axis_positions]
                            if isinstance(axis_info.axis_positions, int)
                            else axis_info.axis_positions,
                            labels=processed_labels,  # Add processed labels information
                            ddl={
                                "cte": "",
                                "queries": [],
                            },  # Initialize DDL field as empty dict, will be filled below
                        )

                    # Log information about the retrieved axis data
                    self.logger.info(
                        f"Number of positions in {obj_key} {obj_name} "
                        f"(ID: {data_dict[obj_key].axis_id}): "
                        f"{data_dict[obj_key].axis_positions}"
                    )

                    # Log information about the retrieved labels
                    if axis_info.labels:
                        # Count labels with single quotes that need escaping
                        escaped_labels_count = sum(
                            1
                            for label in axis_info.labels
                            if label.get("value_group_name")
                            and "'" in label.get("value_group_name", "")
                        )

                        # Log summary information
                        self.logger.info(
                            f"Retrieved {len(axis_info.labels)} labels for {obj_key} {obj_name} "
                            f"({escaped_labels_count} with escaped single quotes)"
                        )
                except Exception as e:
                    self.logger.error(
                        f"Error getting {obj_key} axis data using mg_client: {e}",
                        exc_info=True,
                    )
            else:  # object_type == 'filter'
                # Get the existing FilterData object if it exists
                existing_data = objects.get(obj_key)

                # If the existing object is a FilterData model, update its fields directly
                if isinstance(existing_data, FilterData):
                    # Update the existing object with the new information
                    # Preserve database, type, name, and full_name fields
                    existing_data.ddl = {"cte": "", "queries": []}
                    data_dict[obj_key] = existing_data
                else:
                    # Initialize the filter data structure using FilterData model with minimal info
                    data_dict[obj_key] = FilterData(
                        name=obj_name,
                        ddl={
                            "cte": "",
                            "queries": [],
                        },  # Initialize DDL field as empty dict, will be filled below
                    )
                self.logger.info(f"Initialized filter data for {obj_key}: {obj_name}")

        # Then, fetch DDL information for each object
        populate_method = (
            self._populate_axis_ddl_fields
            if object_type == "axis"
            else self._populate_filter_ddl_fields
        )
        await populate_method(objects, id_panel)
        return data_dict

    async def get_axis_data_with_mg_client(self, axes: Dict, id_panel: int) -> Dict:
        """
        Get axis metadata for all axes using the AsyncMagicGatewayClient's get_axis_info method.
        This method retrieves axis ID, position count, and label information, and also populates DDL information for each axis.

        Args:
            axes: Dictionary of axes configurations (can be strings or AxisData objects)
            id_panel: Panel ID to use for table mapping

        Returns:
            Dictionary with axis data including positions count, labels, and DDL field
        """
        return await self.get_object_data_with_mg_client(axes, id_panel, "axis")

    async def get_filter_data_with_mg_client(
        self, filters: Dict, id_panel: int
    ) -> Dict:
        """
        Get filter metadata and DDL information for all filters.

        Args:
            filters: Dictionary of filter configurations (can be strings or FilterData objects)
            id_panel: Panel ID to use for table mapping

        Returns:
            Dictionary with filter data including DDL field
        """
        return await self.get_object_data_with_mg_client(filters, id_panel, "filter")

    async def get_measures_data_with_mg_client(
        self, facts_axis: Optional[List[FactsData]]
    ) -> List[FactsData]:
        """
        Get measure metadata for all measures in facts_axis using the AsyncMagicGatewayClient.
        This method retrieves measure information from the metadata.ctlg_measures table in PostgreSQL.

        Args:
            facts_axis: List of FactsData models containing measure information (id required)

        Returns:
            List of FactsData models with enriched measure data from PostgreSQL
        """
        if facts_axis is None or not facts_axis:
            return []

        enriched_facts_axis = []

        try:
            # Extract measure IDs from facts_axis
            measure_ids = [str(fact.id) for fact in facts_axis if hasattr(fact, "id")]

            if not measure_ids:
                self.logger.warning("No valid measure IDs found in facts_axis")
                # Return original facts_axis
                return list(facts_axis)

            # Construct a query to get measure data from PostgreSQL
            query = f"""
            SELECT
                id,
                code_name,
                display_name,
                required_facts,
                formula,
                divisor
            FROM metadata.ctlg_measures
            WHERE id IN ({",".join(measure_ids)})
            """

            # Execute the query using the AsyncMagicGatewayClient
            result = await self.mg_client.postgres.execute_query(query)

            # Create a mapping of measure ID to PostgreSQL data
            pg_measures_data = {}
            for row in result.rows:
                measure_id = row.get("id")
                if measure_id is not None:
                    pg_measures_data[str(measure_id)] = row

            # Enrich the original facts_axis with PostgreSQL data
            for fact in facts_axis:
                if not hasattr(fact, "id"):
                    # Skip invalid facts
                    self.logger.warning(f"Skipping invalid fact: {fact}")
                    continue

                fact_id = fact.id

                if str(fact_id) in pg_measures_data:
                    # Add PostgreSQL data
                    pg_data = pg_measures_data[str(fact_id)]

                    # Create a new FactsData with updated data
                    updated_fact = FactsData(
                        id=fact_id,
                        relative_axis=fact.relative_axis,
                        relative_position=fact.relative_position,
                        code_name=pg_data.get("code_name", fact.code_name),
                        display_name=pg_data.get("display_name", fact.display_name),
                        required_facts=pg_data.get(
                            "required_facts", fact.required_facts
                        ),
                        formula=pg_data.get("formula", fact.formula),
                        divisor=pg_data.get("divisor", fact.divisor),
                    )

                    enriched_facts_axis.append(updated_fact)
                else:
                    # If no PostgreSQL data found, keep the original fact
                    enriched_facts_axis.append(fact)

            self.logger.info(
                f"Enriched {len(enriched_facts_axis)} measures with PostgreSQL data"
            )
            return enriched_facts_axis

        except Exception as e:
            self.logger.error(
                f"Error getting measures data using mg_client: {e}", exc_info=True
            )
            # Return the original facts_axis if there's an error
            return list(facts_axis)

    async def get_data_with_mg_client(
        self,
        axes: Dict[str, AxisData],
        filters: Dict[str, FilterData],
        id_panel: int,
        facts_axis: Optional[List[FactsData]] = None,
    ) -> Dict:
        """
        Get metadata for all axes, filters, and measures using the AsyncMagicGatewayClient.
        This method retrieves axis data, filter data, and measures data, and populates DDL information for axes and filters.

        Args:
            axes: Dictionary of axis key to AxisData model
            filters: Dictionary of filter key to FilterData model
            id_panel: Panel ID to use for table mapping
            facts_axis: Optional list of FactsData models containing measure information (id required)

        Returns:
            Dictionary with axes_data, filters_data, and measures_data
        """

        try:
            # Process all data types concurrently for better performance
            tasks = []
            task_types = []  # To track which task is which

            # Add tasks for axes and filters if provided
            if axes:
                tasks.append(self.get_axis_data_with_mg_client(axes, id_panel))
                task_types.append("axes")
            if filters:
                tasks.append(self.get_filter_data_with_mg_client(filters, id_panel))
                task_types.append("filters")
            if facts_axis:
                tasks.append(self.get_measures_data_with_mg_client(facts_axis))
                task_types.append("measures")

            # Run all tasks concurrently if there are any
            if tasks:
                self.logger.info(
                    f"Running {len(tasks)} tasks concurrently: {task_types}"
                )
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results and handle exceptions
                for i, result in enumerate(results):
                    task_type = task_types[i] if i < len(task_types) else f"Task {i}"

                    if isinstance(result, Exception):
                        self.logger.error(
                            f"{task_type} task failed with error: {result}"
                        )
                        # Don't raise the exception, continue processing
                    else:
                        self.logger.info(f"{task_type} task completed successfully")
                        # For measures task, we need to update measures_data directly
                        if task_type == "measures" and isinstance(result, list):
                            facts_axis = result
            else:
                self.logger.warning("No tasks to run")

            # Return the results dictionary
            result_dict = {
                "axes_data": axes,
                "filters_data": filters,
                "measures_data": facts_axis or [],
            }

            self.logger.info(
                f"Returning data with {len(axes)} axes, "
                f"{len(filters)} filters, and {len(facts_axis)} measures"
            )

            return result_dict

        except Exception as e:
            self.logger.error(f"Error in get_data_with_mg_client: {e}", exc_info=True)
            # Return empty data to avoid None errors
            return {
                "axes_data": {},
                "filters_data": {},
                "measures_data": [],
            }

    # These methods have been moved to ValidationProcessor

    # This method has been moved to ValidationProcessor

    # This method has been moved to ValidationProcessor
