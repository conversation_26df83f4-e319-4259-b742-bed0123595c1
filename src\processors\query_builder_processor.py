"""Module for query builder processor."""

from ast import Dict
import copy
import logging
from typing import Any, List, Optional, Tuple, Union
import uuid

from src.core.exceptions import QueryError
from src.models.axis import Period
from src.queries.query_builder import ClickHouseQuery
from src.models.kpi import KPIType
from src.utils.data_processing import calculate_ddl_chunk_indices


class QueryBuilderProcessor:
    """Class to handle query building and validation."""

    def __init__(self, query_builder: ClickHouseQuery):
        """
        Initialize QueryBuilderProcessor.
        Args:
            QueryBuilder: Query builder instance

        Raises:
            ValueError: If any required parameter is None
        """

        # Validate required parameters
        if not query_builder:
            raise ValueError("QueryBuilder is required")

        # Initialize logger and helpers
        self.logger = logging.getLogger(self.__class__.__name__)
        self.query_builder = query_builder

    def _generate_chunk_queries(
        self,
        period: Period,
        kpi_type: KPIType,
        query_id_prefix: str,
    ) -> <PERSON><PERSON>[List[str], List[str], List[Tuple[int, int]]]:
        """
        Generate SQL templates for each chunk and return final queries and query IDs.

        Args:
            period: Period model containing chunking configuration
            kpi_type: Type of KPI to process
            query_id_prefix: Prefix for generating query IDs

        Returns:
            Tuple of (final_queries, final_chunk_query_ids, chunk_indices)
        """
        fourth_axis_data = self.query_builder.axes["fourth_axis"]
        ddl_queries = fourth_axis_data.ddl["queries"]
        total_ddls = len(ddl_queries)

        # Get chunking parameters
        total_positions_product = getattr(period, "total_positions", None)
        threshold = getattr(period, "threshold", None)
        fourth_axis_positions = fourth_axis_data.axis_positions

        # Validate parameters before proceeding
        if total_positions_product is None:
            raise ValueError("total_positions not found in period model")
        if threshold is None:
            raise ValueError("threshold not found in period model")
        if fourth_axis_positions is None or (
            isinstance(fourth_axis_positions, list) and len(fourth_axis_positions) == 0
        ):
            raise ValueError("fourth_axis.axis_positions is None or empty")

        # Extract the actual axis positions value (it's a list, we need the first element)
        if isinstance(fourth_axis_positions, int):
            axis_positions_value = fourth_axis_positions
        elif isinstance(fourth_axis_positions, list) and len(fourth_axis_positions) > 0:
            axis_positions_value = fourth_axis_positions[0]
        else:
            raise ValueError("fourth_axis.axis_positions has invalid type or value")

        if axis_positions_value == 0:
            raise ValueError("fourth_axis.axis_positions value is 0")

        # Calculate chunk indices using the utility function
        chunk_indices = calculate_ddl_chunk_indices(
            total_positions=total_positions_product,
            axis_positions=axis_positions_value,
            threshold=threshold,
            total_ddls=total_ddls,
        )

        # Log chunking information
        other_axes_positions_product = total_positions_product // axis_positions_value
        max_fourth_axis_positions_per_chunk = threshold // other_axes_positions_product
        max_fourth_axis_positions_per_chunk = max(
            1, max_fourth_axis_positions_per_chunk
        )

        self.logger.info(f"Total positions (from period): {total_positions_product}")
        self.logger.info(f"Fourth axis positions: {fourth_axis_positions}")
        self.logger.info(
            f"Other axes positions product: {other_axes_positions_product}"
        )
        self.logger.info(
            f"Max fourth_axis positions per chunk: {max_fourth_axis_positions_per_chunk}"
        )
        self.logger.info(
            f"Splitting {total_ddls} DDL queries into {len(chunk_indices)} chunks"
        )

        # Store original fourth_axis data
        original_fourth_axis = copy.deepcopy(fourth_axis_data)

        final_queries = []
        final_chunk_query_ids = []

        # Generate queries for each chunk
        for chunk_idx, (start_idx, end_idx) in enumerate(chunk_indices):
            chunk_ddls = ddl_queries[start_idx:end_idx]

            # Calculate estimated total positions for this chunk
            estimated_fourth_axis_positions = len(chunk_ddls)
            estimated_total_positions = (
                estimated_fourth_axis_positions * other_axes_positions_product
            )

            self.logger.info(
                f"Generating query for chunk {chunk_idx + 1}/{len(chunk_indices)}: DDLs {start_idx} to {end_idx - 1}"
            )
            self.logger.info(
                f"Estimated positions in chunk: {estimated_fourth_axis_positions} (fourth_axis) * {other_axes_positions_product} (others) = {estimated_total_positions} total"
            )

            # Create a deep copy of the original fourth_axis AxisData
            fourth_axis_copy = copy.deepcopy(original_fourth_axis)
            fourth_axis_copy.ddl["queries"] = chunk_ddls

            # Temporarily set the sliced copy in query_builder
            self.query_builder.axes["fourth_axis"] = fourth_axis_copy

            # Generate final_query_chunk using the sliced fourth_axis
            final_query_chunk = self.query_builder.query_final(
                catman=True if kpi_type == KPIType.CATMAN_KPI else False
            )

            # Generate custom query ID for this chunk's final query
            final_chunk_query_id = (
                f"{query_id_prefix}final_chunk_{chunk_idx}_{uuid.uuid4().hex[:8]}"
            )

            final_queries.append(final_query_chunk)
            final_chunk_query_ids.append(final_chunk_query_id)

        # Restore the original fourth_axis AxisData
        self.query_builder.axes["fourth_axis"] = original_fourth_axis

        return final_queries, final_chunk_query_ids, chunk_indices

    def generate_queries(
        self,
        period: Period,
        kpi_type: KPIType = KPIType.STANDARD_KPI,
        query_id_prefix: str = "query_",
    ) -> Tuple[Dict, Optional[str]]:
        """
        Generate queries based on Jinja2 templates.

        Args:
            period: Period model containing start and end dates
            kpi_type: Type of KPI to process
            query_id_prefix: Prefix for generating query IDs

        Returns:
            Tuple containing:
            - Dict mapping query names to either [query_string, query_id] or [query_strings, query_ids] for chunked queries
            - Optional error message string if an error occurred, None otherwise
        """

        queries: Dict[str, Union[List[str], List[List[str]]]] = {}
        try:
            # Check if period is a Period model
            if not period or not isinstance(period, Period):
                error_msg = f"Invalid period format: {period}. Expected Period model"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            self.logger.info(
                f"Processing period from Period model: {period.label} ({period.date_start} to {period.date_end})"
            )
            self.query_builder.period = period

            # Set max query size - track this step
            queries["max_query_size"] = [
                "SET max_query_size = 100000000;",
                f"{query_id_prefix}max_query_size_{uuid.uuid4().hex[:8]}",
            ]

            # Set max AST size - track this step
            queries["max_ast_elements"] = [
                "SET max_ast_elements = 10000000;",
                f"{query_id_prefix}max_ast_elements_{uuid.uuid4().hex[:8]}",
            ]

            # Set optimization - track this step
            queries["optimize_aggregation_in_order"] = [
                "SET optimize_aggregation_in_order = 1;",
                f"{query_id_prefix}optimize_aggregation_in_order_{uuid.uuid4().hex[:8]}",
            ]

            # Prepare all queries first
            # Create table query with all axes axcept hh and generate custom id
            queries["pre_axis"] = [
                self.query_builder.query_pre_axis(
                    catman=True if kpi_type == KPIType.CATMAN_KPI else False
                ),
                f"{query_id_prefix}pre_axis_{uuid.uuid4().hex[:8]}",
            ]

            # Create axis query with trips aggrigation and generate custom id
            queries["axis"] = [
                self.query_builder.query_axis(),
                f"{query_id_prefix}axis_{uuid.uuid4().hex[:8]}",
            ]

            # Create buyers query and generate custom id
            queries["buyers"] = [
                self.query_builder.query_buyers(
                    catman=True if kpi_type == KPIType.CATMAN_KPI else False
                ),
                f"{query_id_prefix}buyers_{uuid.uuid4().hex[:8]}",
            ]

            # Create BUF query and generate custom id
            queries["buf"] = [
                self.query_builder.query_buf(
                    catman=True if kpi_type == KPIType.CATMAN_KPI else False
                ),
                f"{query_id_prefix}buf_{uuid.uuid4().hex[:8]}",
            ]

            # Create final query and generate custom id for final query and all final chunk queries ids
            if period.split_axis == "fourth_axis":
                final_queries, final_chunk_query_ids, _ = self._generate_chunk_queries(
                    period, kpi_type, query_id_prefix
                )
                queries["final"] = [final_queries, final_chunk_query_ids]
            else:
                queries["final"] = [
                    self.query_builder.query_final(
                        catman=True if kpi_type == KPIType.CATMAN_KPI else False
                    ),
                    f"{query_id_prefix}final_{uuid.uuid4().hex[:8]}",
                ]

            queries["result_table"] = [
                self.query_builder.query_kpi(
                    labels=True,
                    position_numbers=True,
                    catman=True if kpi_type == KPIType.CATMAN_KPI else False,
                ),
                f"{query_id_prefix}result_table_{uuid.uuid4().hex[:8]}",
            ]

        except Exception as e:
            error_msg = f"Failed to generate queries: {e}"
            self.logger.error(error_msg)
            return None, QueryError(error_msg)
        return queries, None
